<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Chat Modals</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #0d1117;
            color: white;
            padding: 20px;
        }
        
        .test-buttons {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .test-btn {
            padding: 15px 25px;
            background: #1976d2;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .test-btn:hover {
            background: #1565c0;
        }
        
        /* Chat Modal Styles */
        .gemini-chat-modal {
            position: fixed;
            inset: 0;
            background: rgba(13, 17, 23, 0.95);
            z-index: 10002;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
            display: none;
        }

        .gemini-chat-modal.open {
            opacity: 1;
            pointer-events: auto;
            display: flex !important;
            align-items: center;
            justify-content: center;
        }

        .gemini-chat-container {
            background: #1a1a1a;
            border-radius: 12px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            position: relative;
        }

        .gemini-close {
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }

        .welcome-text-glow {
            color: #1976d2;
            text-shadow:
                0 0 10px rgba(25, 118, 210, 0.8),
                0 0 20px rgba(25, 118, 210, 0.6),
                0 0 30px rgba(25, 118, 210, 0.4);
            font-weight: 600;
            letter-spacing: 2px;
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }
    </style>
</head>
<body>
    <h1>اختبار واجهات المحادثة</h1>
    
    <div class="test-buttons">
        <button class="test-btn" onclick="showEnergyOptimizationChat()">💡 Energy Optimization</button>
        <button class="test-btn" onclick="showPredictiveMaintenanceChat()">📈 Predictive Maintenance</button>
        <button class="test-btn" onclick="showEnergySecurityChat()">🛡️ Energy Security</button>
        <button class="test-btn" onclick="showCloudEnergyChat()">☁️ Cloud Energy Management</button>
    </div>

    <!-- Energy Optimization Modal -->
    <div id="geminiChatModal" class="gemini-chat-modal">
        <div class="gemini-chat-container">
            <button class="gemini-close" onclick="closeEnergyOptimizationChat()">✕</button>
            <h2 class="welcome-text-glow">ᴡe̥̊しᶜⓞ川𝚎</h2>
            <p>كيف يمكنني مساعدتك في تحسين استهلاك الطاقة اليوم؟</p>
        </div>
    </div>

    <!-- Predictive Maintenance Modal -->
    <div id="predictiveChatModal" class="gemini-chat-modal">
        <div class="gemini-chat-container">
            <button class="gemini-close" onclick="closePredictiveMaintenanceChat()">✕</button>
            <h2 class="welcome-text-glow">ᴡe̥̊しᶜⓞ川𝚎</h2>
            <p>كيف يمكنني مساعدتك في الصيانة التنبؤية اليوم؟</p>
        </div>
    </div>

    <!-- Energy Security Modal -->
    <div id="securityChatModal" class="gemini-chat-modal">
        <div class="gemini-chat-container">
            <button class="gemini-close" onclick="closeEnergySecurityChat()">✕</button>
            <h2 class="welcome-text-glow">ᴡe̥̊しᶜⓞ川𝚎</h2>
            <p>كيف يمكنني مساعدتك في أمان الطاقة اليوم؟</p>
        </div>
    </div>

    <!-- Cloud Energy Management Modal -->
    <div id="cloudChatModal" class="gemini-chat-modal">
        <div class="gemini-chat-container">
            <button class="gemini-close" onclick="closeCloudEnergyChat()">✕</button>
            <h2 class="welcome-text-glow">ᴡe̥̊しᶜⓞ川𝚎</h2>
            <p>كيف يمكنني مساعدتك في إدارة الطاقة السحابية اليوم؟</p>
        </div>
    </div>

    <script>
        // Functions for individual chat modals
        function showEnergyOptimizationChat() {
            console.log('showEnergyOptimizationChat called');
            const modal = document.getElementById('geminiChatModal');
            if (modal) {
                modal.classList.add('open');
                console.log('Energy Optimization modal opened');
            }
        }

        function showPredictiveMaintenanceChat() {
            console.log('showPredictiveMaintenanceChat called');
            const modal = document.getElementById('predictiveChatModal');
            if (modal) {
                modal.classList.add('open');
                console.log('Predictive Maintenance modal opened');
            }
        }

        function showEnergySecurityChat() {
            console.log('showEnergySecurityChat called');
            const modal = document.getElementById('securityChatModal');
            if (modal) {
                modal.classList.add('open');
                console.log('Energy Security modal opened');
            }
        }

        function showCloudEnergyChat() {
            console.log('showCloudEnergyChat called');
            const modal = document.getElementById('cloudChatModal');
            if (modal) {
                modal.classList.add('open');
                console.log('Cloud Energy modal opened');
            }
        }

        // Close functions
        function closeEnergyOptimizationChat() {
            const modal = document.getElementById('geminiChatModal');
            if (modal) {
                modal.classList.remove('open');
            }
        }

        function closePredictiveMaintenanceChat() {
            const modal = document.getElementById('predictiveChatModal');
            if (modal) {
                modal.classList.remove('open');
            }
        }

        function closeEnergySecurityChat() {
            const modal = document.getElementById('securityChatModal');
            if (modal) {
                modal.classList.remove('open');
            }
        }

        function closeCloudEnergyChat() {
            const modal = document.getElementById('cloudChatModal');
            if (modal) {
                modal.classList.remove('open');
            }
        }

        console.log('Test page loaded successfully');
    </script>
</body>
</html>
